#!/usr/bin/env python3
"""
Test script for improved prompts and name extraction
Tests the structured responses and accurate name extraction
"""

import json
import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from resume_parser import ResumeParser
from ollama_client import OllamaClient
from scoring_engine import <PERSON><PERSON><PERSON><PERSON><PERSON>

def test_name_extraction():
    """Test improved name extraction from various resume formats"""
    
    print("🧪 Testing Improved Name Extraction")
    print("=" * 50)
    
    # Test cases with different name formats
    test_resumes = [
        {
            "name": "Standard Format",
            "text": """
<PERSON>
Software Developer
Email: <EMAIL>
Phone: (*************

Experience:
- 3 years Python development
- React and JavaScript experience
            """
        },
        {
            "name": "Name with Middle Initial",
            "text": """
Sarah <PERSON>. <PERSON>
Data Scientist
<EMAIL>

Education:
Master's in Computer Science
            """
        },
        {
            "name": "Name in Header",
            "text": """
ROBERT WILLIAMS
Senior Developer | 5 Years Experience
<EMAIL>
LinkedIn: linkedin.com/in/robertwilliams

Skills: Python, Java, SQL
            """
        },
        {
            "name": "Name with Title",
            "text": """
<PERSON>
Full <PERSON>ack Developer
<EMAIL>

Professional Summary:
Experienced developer with 4+ years...
            """
        },
        {
            "name": "Complex Format",
            "text": """
Dr. James Alexander Thompson III
Senior Software Architect
<EMAIL>
(*************

PROFESSIONAL EXPERIENCE:
Lead Software Architect | TechCorp | 2020-2024
            """
        }
    ]
    
    parser = ResumeParser()
    
    for i, test_case in enumerate(test_resumes, 1):
        print(f"\n📄 Test Case {i}: {test_case['name']}")
        
        # Create mock resume data
        resume_data = {
            'success': True,
            'filename': f'test_resume_{i}.txt',
            'text': test_case['text'],
            'metadata': {},
            'file_size': 1024,
            'file_type': '.txt'
        }
        
        # Extract metadata (including name)
        metadata = parser._extract_metadata(test_case['text'], Path(f'test_{i}.txt'))
        resume_data['metadata'] = metadata
        
        extracted_name = metadata.get('candidate_name', 'Not found')
        print(f"   Extracted Name: {extracted_name}")
        
        # Validate extraction
        if extracted_name != "Name not found" and len(extracted_name.split()) >= 2:
            print("   ✅ Name extraction successful")
        else:
            print("   ❌ Name extraction failed")

def test_structured_llm_response():
    """Test the improved LLM prompt for structured responses"""
    
    print("\n🤖 Testing Structured LLM Responses")
    print("=" * 50)
    
    sample_resume = """
Jennifer Davis
Senior Software Engineer
<EMAIL>
(555) 234-5678
LinkedIn: linkedin.com/in/jenniferdavis
GitHub: github.com/jenniferdavis

PROFESSIONAL EXPERIENCE:
Senior Software Engineer | TechCorp Inc. | 2021-2024
- Developed scalable web applications using Python, Django, and React
- Led a team of 4 developers in agile environment
- Implemented CI/CD pipelines using Jenkins and Docker
- Worked with AWS services (EC2, S3, RDS, Lambda)
- Improved application performance by 35% through optimization

Software Engineer | StartupXYZ | 2019-2021
- Built full-stack applications with JavaScript, Node.js, and React
- Integrated third-party APIs and payment systems
- Collaborated with cross-functional teams using Scrum methodology

EDUCATION:
Master of Science in Computer Science
University of Technology | 2017-2019

Bachelor of Science in Computer Science
Tech University | 2013-2017

SKILLS:
Programming: Python, JavaScript, TypeScript, Java, SQL
Frameworks: Django, React, Node.js, Express, Flask
Databases: PostgreSQL, MongoDB, Redis
Cloud: AWS (EC2, S3, RDS, Lambda), Docker, Kubernetes
Tools: Git, Jenkins, JIRA, VS Code

CERTIFICATIONS:
- AWS Certified Solutions Architect (2023)
- Certified Scrum Master (2022)
"""

    job_description = """
Senior Software Developer Position

REQUIRED SKILLS:
- 3+ years Python development experience
- React.js and JavaScript proficiency
- AWS cloud platform experience
- Database management (PostgreSQL, MongoDB)
- CI/CD pipeline experience
- Team leadership capabilities

PREFERRED QUALIFICATIONS:
- Master's degree in Computer Science
- AWS certifications
- Agile/Scrum experience
- Performance optimization experience
"""

    try:
        print("📡 Testing Ollama client with improved prompts...")
        
        ollama_client = OllamaClient()
        result = ollama_client.analyze_resume(sample_resume, job_description)
        
        if result['success']:
            print("✅ LLM analysis successful")
            analysis = result['analysis']
            
            # Check if response is properly structured
            required_fields = [
                'candidate_name', 'overall_score', 'skills_match', 'experience_relevance',
                'education_match', 'keywords_match', 'overall_fit', 'matching_skills',
                'missing_skills', 'recommendation', 'summary'
            ]
            
            missing_fields = [field for field in required_fields if field not in analysis]
            
            if not missing_fields:
                print("✅ All required fields present in response")
            else:
                print(f"❌ Missing fields: {missing_fields}")
            
            # Check name extraction from LLM
            llm_name = analysis.get('candidate_name', '')
            print(f"   LLM Extracted Name: '{llm_name}'")
            
            if llm_name and llm_name != "Name not found" and len(llm_name.split()) >= 2:
                print("   ✅ LLM name extraction successful")
            else:
                print("   ❌ LLM name extraction failed")
            
            # Check scores are numeric and in range
            score_fields = ['overall_score', 'skills_match', 'experience_relevance', 'education_match']
            valid_scores = True
            
            for field in score_fields:
                score = analysis.get(field, 0)
                if not isinstance(score, (int, float)) or not (0 <= score <= 100):
                    print(f"   ❌ Invalid score for {field}: {score}")
                    valid_scores = False
            
            if valid_scores:
                print("   ✅ All scores are valid (0-100 range)")
            
            # Check recommendation format
            recommendation = analysis.get('recommendation', '')
            if recommendation in ['HIRE', 'CONSIDER', 'REJECT']:
                print("   ✅ Recommendation format is correct")
            else:
                print(f"   ❌ Invalid recommendation: {recommendation}")
            
            # Display sample structured output
            print(f"\n📊 Sample Structured Output:")
            print(f"   Name: {analysis.get('candidate_name', 'N/A')}")
            print(f"   Overall Score: {analysis.get('overall_score', 'N/A')}")
            print(f"   Recommendation: {analysis.get('recommendation', 'N/A')}")
            print(f"   Matching Skills: {len(analysis.get('matching_skills', []))} found")
            print(f"   Missing Skills: {len(analysis.get('missing_skills', []))} identified")
            
        else:
            print(f"❌ LLM analysis failed: {result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ LLM test failed: {str(e)}")
        return False
    
    return True

def test_end_to_end_scoring():
    """Test complete scoring pipeline with improved features"""
    
    print("\n⚡ Testing End-to-End Scoring Pipeline")
    print("=" * 50)
    
    sample_resume_text = """
Michael Chen
Lead Software Developer
<EMAIL>
(555) 345-6789

EXPERIENCE:
Lead Software Developer | InnovaTech | 2020-2024
- Led development of microservices architecture using Python and Docker
- Managed team of 6 developers using Agile methodologies
- Implemented automated testing and CI/CD pipelines
- Optimized database queries improving performance by 40%
- Worked extensively with AWS services and Kubernetes

Software Developer | TechStart | 2018-2020
- Developed web applications using React, Node.js, and PostgreSQL
- Integrated payment systems and third-party APIs
- Participated in code reviews and sprint planning

EDUCATION:
Bachelor of Science in Computer Science
Stanford University | 2014-2018

SKILLS:
Python, JavaScript, React, Node.js, Docker, Kubernetes, AWS, PostgreSQL
"""

    job_description = """
Senior Software Developer Position
Required: Python, JavaScript, React, AWS, team leadership
Experience: 3-5 years
Education: Bachelor's degree preferred
"""

    try:
        # Create resume data
        resume_data = {
            'success': True,
            'filename': 'michael_chen.txt',
            'text': sample_resume_text,
            'metadata': {},
            'file_size': 1500,
            'file_type': '.txt'
        }
        
        # Extract metadata
        parser = ResumeParser()
        metadata = parser._extract_metadata(sample_resume_text, Path('michael_chen.txt'))
        resume_data['metadata'] = metadata
        
        print(f"📄 Parser extracted name: {metadata.get('candidate_name', 'Not found')}")
        
        # Score the resume
        scoring_engine = ScoringEngine()
        result = scoring_engine.score_resume(resume_data, job_description)
        
        if result['success']:
            print("✅ End-to-end scoring successful")
            print(f"   Final Name: {result['metadata'].get('candidate_name', 'Not found')}")
            print(f"   Final Score: {result['final_score']}")
            print(f"   Recommendation: {result['recommendation']}")
            print(f"   Processing Time: {result['processing_time']:.2f}s")
            
            # Check if LLM name was used as fallback
            llm_name = result['analysis'].get('candidate_name', '')
            final_name = result['metadata'].get('candidate_name', '')
            
            print(f"   LLM Name: '{llm_name}'")
            print(f"   Final Name Used: '{final_name}'")
            
            return True
        else:
            print(f"❌ Scoring failed: {result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ End-to-end test failed: {str(e)}")
        return False

def main():
    """Run all improved feature tests"""
    
    print("🚀 Testing Improved Prompts and Name Extraction")
    print("=" * 60)
    
    # Test name extraction
    test_name_extraction()
    
    # Test structured LLM responses
    llm_success = test_structured_llm_response()
    
    # Test end-to-end pipeline
    e2e_success = test_end_to_end_scoring()
    
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY:")
    print(f"   LLM Structured Response: {'✅ PASS' if llm_success else '❌ FAIL'}")
    print(f"   End-to-End Pipeline: {'✅ PASS' if e2e_success else '❌ FAIL'}")
    
    if llm_success and e2e_success:
        print("\n🎉 All tests passed! Improved features are working correctly.")
        print("\n📈 IMPROVEMENTS VERIFIED:")
        print("   • Structured JSON responses from LLM")
        print("   • Accurate name extraction")
        print("   • Consistent scoring format")
        print("   • Proper field validation")
        print("   • Fallback name handling")
    else:
        print("\n❌ Some tests failed. Please check the error messages above.")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
