#!/usr/bin/env python3
"""
Test script for skills-first scoring system
Tests that skills matching is prioritized over experience for real-time recruitment
"""

import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from scoring_engine import ScoringEngine
from config import CRITICAL_SKILLS_THRESHOLD, SKILLS_VETO_THRESHOLD, MINIMUM_SKILLS_PERCENTAGE

def test_skills_veto_system():
    """Test that low skills score results in automatic rejection regardless of experience"""
    
    print("🧪 Testing Skills Veto System")
    print("=" * 50)
    
    # High experience candidate with very low skills
    high_exp_low_skills = {
        'success': True,
        'filename': 'high_exp_low_skills.txt',
        'text': """
        Senior Manager
        John Executive
        <EMAIL>
        
        EXPERIENCE:
        Senior Project Manager | BigCorp | 2015-2024
        - Managed teams of 50+ people
        - Led $10M+ projects
        - 15+ years management experience
        - Strategic planning and execution
        - Budget management and cost optimization
        
        SKILLS:
        Leadership, Project Management, Strategic Planning, Budget Management
        
        EDUCATION:
        MBA in Business Administration
        """,
        'metadata': {'candidate_name': '<PERSON>'}
    }
    
    # Job requiring technical skills
    tech_job_description = """
    Senior Software Developer Position
    
    REQUIRED SKILLS:
    - Python (5+ years)
    - React
    - Node.js
    - PostgreSQL
    - AWS
    - Docker
    
    EXPERIENCE: 3-5 years software development
    """
    
    try:
        scoring_engine = ScoringEngine(thread_pool_size=1)
        result = scoring_engine.score_resume(high_exp_low_skills, tech_job_description)
        
        if result['success']:
            skills_score = result['scores']['skills_match']
            final_score = result['final_score']
            recommendation = result['recommendation']
            
            print(f"📊 High Experience, Low Skills Candidate:")
            print(f"   Skills Score: {skills_score}")
            print(f"   Final Score: {final_score}")
            print(f"   Recommendation: {recommendation}")
            
            # Should be rejected due to skills veto
            if recommendation == "REJECT" and skills_score < SKILLS_VETO_THRESHOLD:
                print("   ✅ CORRECT: High experience candidate rejected due to low skills")
                return True
            else:
                print("   ❌ INCORRECT: High experience candidate should be rejected for low skills")
                return False
        else:
            print(f"❌ Scoring failed: {result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ Skills veto test failed: {str(e)}")
        return False

def test_skills_priority_scoring():
    """Test that skills matching is prioritized in scoring"""
    
    print("\n🎯 Testing Skills Priority Scoring")
    print("=" * 50)
    
    # High skills, moderate experience candidate
    high_skills_candidate = {
        'success': True,
        'filename': 'high_skills.txt',
        'text': """
        Sarah Developer
        Software Engineer
        <EMAIL>
        
        EXPERIENCE:
        Software Engineer | TechStart | 2022-2024
        - 2 years Python development
        - Built React applications
        - Worked with PostgreSQL and AWS
        - Implemented Docker containers
        - Node.js backend development
        
        SKILLS:
        Python, React, Node.js, PostgreSQL, AWS, Docker, JavaScript, Git
        
        EDUCATION:
        Bachelor's in Computer Science
        """,
        'metadata': {'candidate_name': 'Sarah Developer'}
    }
    
    # Moderate skills, high experience candidate
    moderate_skills_candidate = {
        'success': True,
        'filename': 'moderate_skills.txt',
        'text': """
        Mike Senior
        Senior Developer
        <EMAIL>
        
        EXPERIENCE:
        Senior Developer | OldTech | 2015-2024
        - 9 years software development
        - Team leadership experience
        - Project management
        - Legacy system maintenance
        - Database administration
        
        SKILLS:
        Java, SQL, Oracle, XML, SOAP, Legacy Systems
        
        EDUCATION:
        Master's in Computer Science
        """,
        'metadata': {'candidate_name': 'Mike Senior'}
    }
    
    job_description = """
    Software Developer Position
    
    REQUIRED SKILLS:
    - Python
    - React
    - Node.js
    - PostgreSQL
    - AWS
    - Docker
    """
    
    try:
        scoring_engine = ScoringEngine(thread_pool_size=1)
        
        # Score high skills candidate
        result1 = scoring_engine.score_resume(high_skills_candidate, job_description)
        # Score moderate skills candidate  
        result2 = scoring_engine.score_resume(moderate_skills_candidate, job_description)
        
        if result1['success'] and result2['success']:
            print(f"📊 High Skills Candidate (2 years exp):")
            print(f"   Skills Score: {result1['scores']['skills_match']}")
            print(f"   Final Score: {result1['final_score']}")
            print(f"   Recommendation: {result1['recommendation']}")
            
            print(f"\n📊 Moderate Skills Candidate (9 years exp):")
            print(f"   Skills Score: {result2['scores']['skills_match']}")
            print(f"   Final Score: {result2['final_score']}")
            print(f"   Recommendation: {result2['recommendation']}")
            
            # High skills candidate should score better despite less experience
            if result1['final_score'] > result2['final_score']:
                print("\n   ✅ CORRECT: High skills candidate scored higher despite less experience")
                return True
            else:
                print("\n   ❌ INCORRECT: Skills should be prioritized over experience")
                return False
        else:
            print("❌ One or both scoring attempts failed")
            return False
            
    except Exception as e:
        print(f"❌ Skills priority test failed: {str(e)}")
        return False

def test_skills_coverage_analysis():
    """Test skills coverage analysis and thresholds"""
    
    print("\n📈 Testing Skills Coverage Analysis")
    print("=" * 50)
    
    test_cases = [
        {
            'name': 'Perfect Skills Match',
            'skills': 'Python, React, Node.js, PostgreSQL, AWS, Docker',
            'expected_coverage': 100,
            'expected_level': 'EXCELLENT'
        },
        {
            'name': 'Good Skills Match',
            'skills': 'Python, React, PostgreSQL, AWS',
            'expected_coverage': 67,  # 4 out of 6
            'expected_level': 'GOOD'
        },
        {
            'name': 'Marginal Skills Match',
            'skills': 'Python, React',
            'expected_coverage': 33,  # 2 out of 6
            'expected_level': 'POOR'
        },
        {
            'name': 'No Skills Match',
            'skills': 'Java, C++, Oracle',
            'expected_coverage': 0,
            'expected_level': 'POOR'
        }
    ]
    
    job_description = """
    REQUIRED SKILLS:
    - Python
    - React  
    - Node.js
    - PostgreSQL
    - AWS
    - Docker
    """
    
    try:
        scoring_engine = ScoringEngine(thread_pool_size=1)
        
        for i, test_case in enumerate(test_cases, 1):
            resume_text = f"""
            Test Candidate {i}
            Developer
            test{i}@email.com
            
            SKILLS: {test_case['skills']}
            
            EXPERIENCE:
            - 3 years development experience
            """
            
            resume_data = {
                'success': True,
                'filename': f'test_case_{i}.txt',
                'text': resume_text,
                'metadata': {'candidate_name': f'Test Candidate {i}'}
            }
            
            result = scoring_engine.score_resume(resume_data, job_description)
            
            if result['success']:
                analysis = result['analysis']
                coverage = analysis.get('skills_coverage_percentage', 0)
                level = analysis.get('skills_coverage_level', 'UNKNOWN')
                meets_minimum = analysis.get('meets_minimum_skills', False)
                
                print(f"\n📊 {test_case['name']}:")
                print(f"   Skills Coverage: {coverage:.1f}%")
                print(f"   Coverage Level: {level}")
                print(f"   Meets Minimum: {meets_minimum}")
                print(f"   Final Score: {result['final_score']}")
                print(f"   Recommendation: {result['recommendation']}")
                
                # Validate coverage calculation
                expected_range = (test_case['expected_coverage'] - 10, test_case['expected_coverage'] + 10)
                if expected_range[0] <= coverage <= expected_range[1]:
                    print(f"   ✅ Coverage calculation correct")
                else:
                    print(f"   ❌ Coverage calculation incorrect (expected ~{test_case['expected_coverage']}%)")
            else:
                print(f"❌ Test case {i} failed: {result['error']}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Skills coverage test failed: {str(e)}")
        return False

def test_real_world_scenarios():
    """Test real-world recruitment scenarios"""
    
    print("\n🌍 Testing Real-World Recruitment Scenarios")
    print("=" * 50)
    
    scenarios = [
        {
            'name': 'Perfect Match Candidate',
            'resume': """
            Alice Perfect
            Senior Python Developer
            <EMAIL>
            
            EXPERIENCE:
            Senior Python Developer | TechCorp | 2020-2024
            - 4+ years Python development
            - Built scalable React applications
            - Designed PostgreSQL databases
            - Deployed on AWS with Docker
            - Led team using Agile methodologies
            
            SKILLS: Python, React, Node.js, PostgreSQL, AWS, Docker, Git, Agile
            """,
            'expected_recommendation': 'HIRE'
        },
        {
            'name': 'High Experience, Wrong Skills',
            'resume': """
            Bob Wrong
            Senior Java Developer
            <EMAIL>
            
            EXPERIENCE:
            Senior Java Developer | BigCorp | 2010-2024
            - 14 years Java development
            - Enterprise applications
            - Oracle database expert
            - Team leadership
            - Architecture design
            
            SKILLS: Java, Spring, Oracle, Hibernate, Maven, Jenkins
            """,
            'expected_recommendation': 'REJECT'
        },
        {
            'name': 'Junior with Right Skills',
            'resume': """
            Carol Junior
            Junior Developer
            <EMAIL>
            
            EXPERIENCE:
            Junior Developer | StartupXYZ | 2023-2024
            - 1 year Python development
            - Built React components
            - Worked with PostgreSQL
            - Basic AWS deployment
            
            SKILLS: Python, React, PostgreSQL, AWS, JavaScript, HTML, CSS
            """,
            'expected_recommendation': 'CONSIDER'
        }
    ]
    
    job_description = """
    Python Developer Position
    
    REQUIRED SKILLS:
    - Python (2+ years)
    - React
    - PostgreSQL
    - AWS
    
    EXPERIENCE: 2-5 years preferred
    """
    
    try:
        scoring_engine = ScoringEngine(thread_pool_size=1)
        
        for scenario in scenarios:
            resume_data = {
                'success': True,
                'filename': f"{scenario['name'].lower().replace(' ', '_')}.txt",
                'text': scenario['resume'],
                'metadata': {'candidate_name': scenario['name'].split()[0] + ' ' + scenario['name'].split()[1]}
            }
            
            result = scoring_engine.score_resume(resume_data, job_description)
            
            if result['success']:
                print(f"\n📊 {scenario['name']}:")
                print(f"   Skills Score: {result['scores']['skills_match']}")
                print(f"   Final Score: {result['final_score']}")
                print(f"   Recommendation: {result['recommendation']}")
                print(f"   Expected: {scenario['expected_recommendation']}")
                
                if result['recommendation'] == scenario['expected_recommendation']:
                    print(f"   ✅ CORRECT recommendation")
                else:
                    print(f"   ❌ INCORRECT recommendation")
                    return False
            else:
                print(f"❌ Scenario '{scenario['name']}' failed: {result['error']}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Real-world scenarios test failed: {str(e)}")
        return False

def main():
    """Run all skills-first scoring tests"""
    
    print("🎯 Skills-First Scoring System Test Suite")
    print("=" * 60)
    
    # Test skills veto system
    veto_success = test_skills_veto_system()
    
    # Test skills priority
    priority_success = test_skills_priority_scoring()
    
    # Test skills coverage analysis
    coverage_success = test_skills_coverage_analysis()
    
    # Test real-world scenarios
    scenarios_success = test_real_world_scenarios()
    
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY:")
    print(f"   Skills Veto System: {'✅ PASS' if veto_success else '❌ FAIL'}")
    print(f"   Skills Priority Scoring: {'✅ PASS' if priority_success else '❌ FAIL'}")
    print(f"   Skills Coverage Analysis: {'✅ PASS' if coverage_success else '❌ FAIL'}")
    print(f"   Real-World Scenarios: {'✅ PASS' if scenarios_success else '❌ FAIL'}")
    
    all_passed = veto_success and priority_success and coverage_success and scenarios_success
    
    if all_passed:
        print("\n🎉 All skills-first scoring tests passed!")
        print("\n📈 SKILLS-FIRST SYSTEM VERIFIED:")
        print("   • Skills matching is now 50% of total score")
        print("   • Automatic rejection for skills < 30%")
        print("   • High experience cannot compensate for missing skills")
        print("   • Skills coverage analysis and thresholds")
        print("   • Real-time recruitment optimized scoring")
        print("\n💡 RECRUITMENT BENEFITS:")
        print("   • Prevents hiring wrong-skilled candidates")
        print("   • Prioritizes technical fit over experience")
        print("   • Accurate skills gap analysis")
        print("   • Optimized for technical roles")
    else:
        print("\n❌ Some tests failed. Please check the error messages above.")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
