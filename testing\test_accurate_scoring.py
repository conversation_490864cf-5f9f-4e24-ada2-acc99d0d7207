#!/usr/bin/env python3
"""
Test script for accurate scoring and skill matching
Tests the improved skill extraction and numerical-only scoring
"""

import json
import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from scoring_engine import ScoringEngine
from ollama_client import OllamaClient

def test_accurate_skill_matching():
    """Test accurate skill matching with real examples"""
    
    print("🧪 Testing Accurate Skill Matching")
    print("=" * 50)
    
    # Test case with specific skills
    resume_text = """
    John Smith
    Software Developer
    <EMAIL>
    
    SKILLS:
    Programming Languages: Python, JavaScript, Java
    Web Technologies: React, Node.js, HTML, CSS
    Databases: PostgreSQL, MongoDB
    Cloud: AWS, Docker
    Tools: Git, JIRA
    
    EXPERIENCE:
    - 3 years Python development
    - Built React applications
    - Worked with PostgreSQL databases
    - Deployed on AWS using Docker
    """
    
    job_description = """
    Software Developer Position
    
    REQUIRED SKILLS:
    - Python (3+ years)
    - JavaScript
    - React
    - PostgreSQL
    - AWS
    - Git
    
    PREFERRED SKILLS:
    - TypeScript
    - Kubernetes
    - GraphQL
    """
    
    try:
        scoring_engine = ScoringEngine(thread_pool_size=1)
        
        # Test skill extraction from resume
        resume_skills = scoring_engine._extract_skills_from_text(resume_text)
        print(f"📄 Skills found in resume: {resume_skills}")
        
        # Test skill extraction from job description
        job_skills = scoring_engine._extract_skills_from_text(job_description)
        print(f"💼 Skills required in job: {job_skills}")
        
        # Test matching logic
        matching_skills = []
        missing_skills = []
        
        for job_skill in job_skills:
            found = False
            for resume_skill in resume_skills:
                if job_skill.lower() == resume_skill.lower():
                    matching_skills.append(resume_skill)
                    found = True
                    break
            if not found:
                missing_skills.append(job_skill)
        
        print(f"✅ Matching skills: {matching_skills}")
        print(f"❌ Missing skills: {missing_skills}")
        
        # Calculate accuracy
        if job_skills:
            accuracy = len(matching_skills) / len(job_skills) * 100
            print(f"📊 Skill match accuracy: {accuracy:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ Skill matching test failed: {str(e)}")
        return False

def test_numerical_scoring_only():
    """Test that LLM returns only numerical scores without explanations"""
    
    print("\n🔢 Testing Numerical-Only Scoring")
    print("=" * 50)
    
    sample_resume = """
    Alice Johnson
    Senior Python Developer
    <EMAIL>
    
    EXPERIENCE:
    Senior Python Developer | TechCorp | 2020-2024
    - Developed web applications using Python and Django
    - Built REST APIs with Flask
    - Worked with PostgreSQL and Redis
    - Deployed applications on AWS using Docker
    - Led team of 3 developers
    
    SKILLS:
    Python, Django, Flask, PostgreSQL, Redis, AWS, Docker, Git, React
    
    EDUCATION:
    Master's in Computer Science
    """
    
    job_description = """
    Senior Python Developer Position
    
    REQUIRED:
    - Python (5+ years)
    - Django or Flask
    - PostgreSQL
    - AWS
    - Team leadership
    
    PREFERRED:
    - Docker
    - React
    - Redis
    """
    
    try:
        ollama_client = OllamaClient(thread_pool_size=1)
        
        print("📡 Testing LLM response format...")
        result = ollama_client.analyze_resume(sample_resume, job_description)
        
        if result['success']:
            analysis = result['analysis']
            
            print("✅ LLM analysis successful")
            
            # Check if response contains only numerical scores
            score_fields = ['overall_score', 'skills_match', 'experience_relevance', 
                          'education_match', 'keywords_match', 'overall_fit', 'growth_potential']
            
            print("\n📊 Numerical Scores:")
            for field in score_fields:
                score = analysis.get(field, 'N/A')
                print(f"   {field}: {score}")
                
                # Validate it's a number
                if isinstance(score, (int, float)) and 0 <= score <= 100:
                    print(f"      ✅ Valid numerical score")
                else:
                    print(f"      ❌ Invalid score format")
            
            # Check skill matching accuracy
            print(f"\n🎯 Skill Analysis:")
            print(f"   Matching skills: {analysis.get('matching_skills', [])}")
            print(f"   Missing skills: {analysis.get('missing_skills', [])}")
            
            # Verify skills are actually in resume
            resume_lower = sample_resume.lower()
            matching_skills = analysis.get('matching_skills', [])
            
            print(f"\n🔍 Skill Verification:")
            for skill in matching_skills:
                if skill.lower() in resume_lower:
                    print(f"   ✅ '{skill}' found in resume")
                else:
                    print(f"   ❌ '{skill}' NOT found in resume")
            
            return True
        else:
            print(f"❌ LLM analysis failed: {result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ Numerical scoring test failed: {str(e)}")
        return False

def test_end_to_end_accuracy():
    """Test complete scoring pipeline with accuracy focus"""
    
    print("\n⚡ Testing End-to-End Accuracy")
    print("=" * 50)
    
    # Create test resume with specific skills
    resume_data = {
        'success': True,
        'filename': 'test_accuracy.txt',
        'text': """
        Michael Chen
        Full Stack Developer
        <EMAIL>
        
        TECHNICAL SKILLS:
        Languages: Python, JavaScript, TypeScript
        Frontend: React, HTML5, CSS3
        Backend: Node.js, Django, Express
        Databases: PostgreSQL, MongoDB
        Cloud: AWS, Docker
        Tools: Git, Jenkins
        
        EXPERIENCE:
        Full Stack Developer | StartupXYZ | 2021-2024
        - Built web applications using React and Node.js
        - Developed REST APIs with Django and Express
        - Managed PostgreSQL and MongoDB databases
        - Deployed applications on AWS with Docker
        - Implemented CI/CD pipelines with Jenkins
        
        EDUCATION:
        Bachelor's in Computer Science
        """,
        'metadata': {
            'candidate_name': 'Michael Chen',
            'word_count': 120,
            'has_email': True,
            'has_phone': False
        }
    }
    
    job_description = """
    Full Stack Developer Position
    
    REQUIRED SKILLS:
    - JavaScript (3+ years)
    - React
    - Node.js
    - PostgreSQL
    - AWS
    
    PREFERRED SKILLS:
    - TypeScript
    - Docker
    - Python
    - MongoDB
    """
    
    try:
        scoring_engine = ScoringEngine(thread_pool_size=1)
        
        print("🔄 Processing resume with improved scoring...")
        result = scoring_engine.score_resume(resume_data, job_description)
        
        if result['success']:
            print("✅ End-to-end scoring successful")
            
            analysis = result['analysis']
            
            print(f"\n👤 Candidate: {result['metadata'].get('candidate_name', 'N/A')}")
            print(f"📊 Final Score: {result['final_score']}")
            print(f"🎯 Recommendation: {result['recommendation']}")
            
            print(f"\n📈 Score Breakdown:")
            scores = result['scores']
            for criterion, score in scores.items():
                print(f"   {criterion}: {score}")
            
            print(f"\n🎯 Accurate Skill Matching:")
            matching_skills = analysis.get('matching_skills', [])
            missing_skills = analysis.get('missing_skills', [])
            
            print(f"   ✅ Matching skills ({len(matching_skills)}): {matching_skills}")
            print(f"   ❌ Missing skills ({len(missing_skills)}): {missing_skills}")
            
            # Verify accuracy
            resume_text = resume_data['text'].lower()
            
            print(f"\n🔍 Verification:")
            accurate_matches = 0
            for skill in matching_skills:
                if skill.lower() in resume_text:
                    accurate_matches += 1
                    print(f"   ✅ '{skill}' correctly identified")
                else:
                    print(f"   ❌ '{skill}' incorrectly identified")
            
            accuracy = (accurate_matches / len(matching_skills) * 100) if matching_skills else 100
            print(f"\n📊 Skill Matching Accuracy: {accuracy:.1f}%")
            
            return accuracy >= 90  # Expect 90%+ accuracy
        else:
            print(f"❌ End-to-end scoring failed: {result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ End-to-end test failed: {str(e)}")
        return False

def main():
    """Run all accuracy tests"""
    
    print("🎯 Accurate Scoring and Skill Matching Test Suite")
    print("=" * 60)
    
    # Test skill matching accuracy
    skill_success = test_accurate_skill_matching()
    
    # Test numerical-only scoring
    numerical_success = test_numerical_scoring_only()
    
    # Test end-to-end accuracy
    e2e_success = test_end_to_end_accuracy()
    
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY:")
    print(f"   Skill Matching Accuracy: {'✅ PASS' if skill_success else '❌ FAIL'}")
    print(f"   Numerical-Only Scoring: {'✅ PASS' if numerical_success else '❌ FAIL'}")
    print(f"   End-to-End Accuracy: {'✅ PASS' if e2e_success else '❌ FAIL'}")
    
    if skill_success and numerical_success and e2e_success:
        print("\n🎉 All accuracy tests passed!")
        print("\n📈 IMPROVEMENTS VERIFIED:")
        print("   • Accurate skill extraction from resume text")
        print("   • Precise skill matching against job requirements")
        print("   • Numerical-only scoring without explanations")
        print("   • Rule-based validation of LLM results")
        print("   • Comprehensive skill database matching")
        print("\n💡 FEATURES:")
        print("   • Only skills actually in resume are listed as 'matching'")
        print("   • Only required skills not in resume are listed as 'missing'")
        print("   • Scores are pure numbers (0-100) without text")
        print("   • Case-insensitive skill matching")
        print("   • Exact word boundary matching")
    else:
        print("\n❌ Some tests failed. Please check the error messages above.")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
