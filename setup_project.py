#!/usr/bin/env python3
"""
Project Setup Script
Creates sample data and sets up the resume parser environment
"""

import os
import sys
from pathlib import Path
import subprocess

def create_sample_resumes():
    """Create sample resume files for testing"""
    
    resumes_dir = Path("resumes")
    resumes_dir.mkdir(exist_ok=True)
    
    sample_resumes = {
        "john_doe_software_dev.txt": """
John Doe
Software Developer
Email: <EMAIL>
Phone: (*************
LinkedIn: linkedin.com/in/johndoe
GitHub: github.com/johndoe

EXPERIENCE:
Senior Software Developer | TechCorp Inc. | 2021-2024
- Developed web applications using Python, Django, and React
- Implemented RESTful APIs and microservices architecture
- Worked with PostgreSQL databases and AWS cloud services
- Led a team of 3 junior developers in agile environment
- Improved application performance by 40% through optimization

Software Developer | StartupXYZ | 2019-2021
- Built full-stack applications with JavaScript, Node.js, and React
- Integrated third-party APIs and payment systems
- Collaborated with cross-functional teams using Scrum methodology
- Deployed applications using Docker and Kubernetes

EDUCATION:
Bachelor of Science in Computer Science
University of Technology | 2015-2019
GPA: 3.8/4.0

SKILLS:
Programming Languages: Python, JavaScript, Java, SQL
Frameworks: Django, React, Node.js, Express
Databases: PostgreSQL, MongoDB, Redis
Cloud: AWS (EC2, S3, RDS), Docker, Kubernetes
Tools: Git, Jenkins, JIRA, VS Code

CERTIFICATIONS:
- AWS Certified Developer Associate (2023)
- Certified Scrum Master (2022)
""",

        "jane_smith_data_scientist.txt": """
Jane Smith
Data Scientist
Email: <EMAIL>
Phone: (*************
LinkedIn: linkedin.com/in/janesmith

EXPERIENCE:
Senior Data Scientist | DataTech Solutions | 2022-2024
- Developed machine learning models for predictive analytics
- Worked with large datasets using Python, pandas, and scikit-learn
- Created data visualizations and dashboards using Tableau
- Implemented deep learning models with TensorFlow and PyTorch
- Collaborated with business stakeholders to define requirements

Data Analyst | Analytics Corp | 2020-2022
- Performed statistical analysis on customer behavior data
- Built automated reporting systems using Python and SQL
- Created A/B testing frameworks for product optimization
- Worked with cloud platforms (AWS, Google Cloud)

EDUCATION:
Master of Science in Data Science
Data University | 2018-2020

Bachelor of Science in Statistics
Math College | 2014-2018

SKILLS:
Programming: Python, R, SQL, Scala
Machine Learning: scikit-learn, TensorFlow, PyTorch, Keras
Data Tools: pandas, NumPy, Jupyter, Apache Spark
Visualization: Tableau, matplotlib, seaborn, Plotly
Databases: PostgreSQL, MongoDB, Cassandra
Cloud: AWS, Google Cloud Platform, Azure

PROJECTS:
- Customer Churn Prediction Model (95% accuracy)
- Real-time Fraud Detection System
- Recommendation Engine for E-commerce Platform
""",

        "bob_wilson_frontend_dev.txt": """
Bob Wilson
Frontend Developer
Email: <EMAIL>
Phone: (*************
Portfolio: bobwilson.dev

EXPERIENCE:
Frontend Developer | WebDesign Studio | 2022-2024
- Developed responsive web applications using React and TypeScript
- Implemented modern UI/UX designs with CSS3 and Sass
- Optimized website performance and accessibility
- Collaborated with backend developers for API integration
- Used Git for version control and agile development practices

Junior Web Developer | Creative Agency | 2021-2022
- Built websites using HTML5, CSS3, and JavaScript
- Worked with content management systems (WordPress)
- Assisted in mobile-first responsive design implementation
- Participated in code reviews and team meetings

EDUCATION:
Associate Degree in Web Development
Community College | 2019-2021

SKILLS:
Frontend: HTML5, CSS3, JavaScript, TypeScript, React, Vue.js
Styling: Sass, Less, Bootstrap, Tailwind CSS
Tools: Webpack, Vite, npm, Git, VS Code
Design: Figma, Adobe XD, Photoshop
Testing: Jest, Cypress, React Testing Library

PROJECTS:
- E-commerce Website with React and Redux
- Portfolio Website with Next.js
- Mobile App UI with React Native
""",

        "alice_johnson_project_manager.txt": """
Alice Johnson
Project Manager
Email: <EMAIL>
Phone: (*************
LinkedIn: linkedin.com/in/alicejohnson

EXPERIENCE:
Senior Project Manager | TechSolutions Inc. | 2020-2024
- Managed software development projects with teams of 10-15 people
- Implemented Agile and Scrum methodologies
- Coordinated with stakeholders and clients for requirement gathering
- Managed project budgets ranging from $100K to $500K
- Delivered 95% of projects on time and within budget

Project Coordinator | Innovation Labs | 2018-2020
- Assisted in project planning and execution
- Organized team meetings and sprint planning sessions
- Tracked project progress using JIRA and Confluence
- Facilitated communication between development and business teams

EDUCATION:
Master of Business Administration (MBA)
Business School | 2016-2018

Bachelor of Science in Information Technology
Tech University | 2012-2016

SKILLS:
Project Management: Agile, Scrum, Waterfall, Kanban
Tools: JIRA, Confluence, Microsoft Project, Trello
Communication: Stakeholder management, Team leadership
Technical: Basic understanding of software development lifecycle
Certifications: PMP, Certified Scrum Master

ACHIEVEMENTS:
- Led digital transformation project saving company $200K annually
- Improved team productivity by 30% through process optimization
- Successfully managed remote teams across different time zones
""",

        "mike_davis_devops.txt": """
Mike Davis
DevOps Engineer
Email: <EMAIL>
Phone: (*************
GitHub: github.com/mikedavis

EXPERIENCE:
DevOps Engineer | CloudTech Systems | 2021-2024
- Designed and implemented CI/CD pipelines using Jenkins and GitLab
- Managed AWS infrastructure with Terraform and CloudFormation
- Containerized applications using Docker and orchestrated with Kubernetes
- Implemented monitoring and logging solutions with Prometheus and ELK stack
- Automated deployment processes reducing deployment time by 60%

Systems Administrator | ServerCorp | 2019-2021
- Maintained Linux servers and network infrastructure
- Implemented backup and disaster recovery procedures
- Monitored system performance and resolved technical issues
- Managed database systems (MySQL, PostgreSQL)

EDUCATION:
Bachelor of Science in Computer Engineering
Engineering College | 2015-2019

SKILLS:
Cloud Platforms: AWS, Azure, Google Cloud Platform
Containerization: Docker, Kubernetes, OpenShift
CI/CD: Jenkins, GitLab CI, GitHub Actions
Infrastructure as Code: Terraform, Ansible, CloudFormation
Monitoring: Prometheus, Grafana, ELK Stack, Nagios
Operating Systems: Linux (Ubuntu, CentOS), Windows Server
Scripting: Bash, Python, PowerShell

CERTIFICATIONS:
- AWS Certified Solutions Architect
- Certified Kubernetes Administrator (CKA)
- Docker Certified Associate
"""
    }
    
    for filename, content in sample_resumes.items():
        file_path = resumes_dir / filename
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content.strip())
        print(f"✅ Created sample resume: {filename}")
    
    return len(sample_resumes)

def create_sample_job_descriptions():
    """Create sample job description files"""
    
    job_descriptions = {
        "software_developer_job.txt": """
Senior Software Developer Position - TechCorp Solutions

POSITION OVERVIEW:
We are seeking a highly skilled Senior Software Developer to join our dynamic engineering team. The ideal candidate will have strong technical expertise, excellent problem-solving abilities, and a passion for building scalable software solutions.

REQUIRED TECHNICAL SKILLS:
- Programming Languages: Python (3+ years), JavaScript (ES6+), TypeScript
- Web Frameworks: React.js, Node.js, Express.js, Django/Flask
- Database Technologies: PostgreSQL, MongoDB, Redis
- Cloud Platforms: AWS (EC2, S3, RDS, Lambda), Docker containerization
- Version Control: Git, GitHub/GitLab workflows
- API Development: RESTful APIs, GraphQL
- Testing: Unit testing, Integration testing, TDD practices

REQUIRED EXPERIENCE:
- 3-5 years of professional software development experience
- Experience with Agile/Scrum development methodologies
- Proven track record of delivering production-ready applications
- Experience with CI/CD pipelines and DevOps practices
- Strong understanding of software architecture patterns
- Experience with code reviews and mentoring junior developers

REQUIRED EDUCATION & CERTIFICATIONS:
- Bachelor's degree in Computer Science, Software Engineering, or related technical field
- Relevant certifications (AWS, Azure, or Google Cloud) preferred

PREFERRED QUALIFICATIONS:
- Master's degree in Computer Science or related field
- Experience with microservices architecture
- Knowledge of machine learning frameworks (TensorFlow, PyTorch)
- Experience with mobile development (React Native, Flutter)
- Familiarity with blockchain technologies
- Open source contributions

SOFT SKILLS:
- Excellent communication and collaboration skills
- Strong analytical and problem-solving abilities
- Ability to work independently and in team environments
- Leadership potential and mentoring capabilities
- Adaptability to new technologies and frameworks

RESPONSIBILITIES:
- Design, develop, and maintain scalable web applications
- Collaborate with cross-functional teams (Product, Design, QA)
- Participate in architectural decisions and technical planning
- Conduct code reviews and ensure code quality standards
- Troubleshoot and resolve complex technical issues
- Mentor junior developers and contribute to team knowledge sharing
- Stay updated with emerging technologies and industry best practices

COMPANY BENEFITS:
- Competitive salary range: $90,000 - $130,000
- Comprehensive health, dental, and vision insurance
- 401(k) with company matching
- Flexible work arrangements (hybrid/remote options)
- Professional development budget ($2,000 annually)
- Stock options and performance bonuses
- 25 days PTO + holidays
""",

        "data_scientist_job.txt": """
Senior Data Scientist Position - DataTech Analytics

POSITION OVERVIEW:
We are seeking an experienced Senior Data Scientist to lead advanced analytics initiatives and drive data-driven decision making across our organization. The ideal candidate will have deep expertise in machine learning, statistical modeling, and business intelligence.

REQUIRED TECHNICAL SKILLS:
- Programming Languages: Python (4+ years), R, SQL, Scala
- Machine Learning: scikit-learn, TensorFlow, PyTorch, Keras, XGBoost
- Data Processing: pandas, NumPy, Apache Spark, Hadoop
- Visualization: Tableau, Power BI, matplotlib, seaborn, Plotly
- Databases: PostgreSQL, MongoDB, Cassandra, Snowflake
- Cloud Platforms: AWS (SageMaker, Redshift), Google Cloud (BigQuery), Azure
- MLOps: Docker, Kubernetes, MLflow, Airflow
- Statistical Analysis: Hypothesis testing, A/B testing, experimental design

REQUIRED EXPERIENCE:
- 4-6 years of professional data science experience
- Proven track record of deploying ML models to production
- Experience with end-to-end ML pipeline development
- Strong background in statistical modeling and analysis
- Experience with big data technologies and distributed computing
- Business stakeholder collaboration and requirement gathering

REQUIRED EDUCATION & CERTIFICATIONS:
- Master's degree in Data Science, Statistics, Computer Science, or related quantitative field
- PhD preferred for research-focused roles
- Relevant certifications (AWS ML, Google Cloud ML, Microsoft Azure AI) preferred

PREFERRED QUALIFICATIONS:
- Experience with deep learning and neural networks
- Knowledge of natural language processing (NLP) and computer vision
- Experience with time series forecasting and optimization
- Familiarity with reinforcement learning
- Published research papers or open source contributions
- Experience with real-time data processing and streaming

SOFT SKILLS:
- Excellent analytical and problem-solving abilities
- Strong communication skills for technical and non-technical audiences
- Business acumen and strategic thinking
- Leadership and mentoring capabilities
- Curiosity and continuous learning mindset

RESPONSIBILITIES:
- Design and implement advanced machine learning models and algorithms
- Lead data science projects from conception to production deployment
- Collaborate with engineering teams to build scalable ML infrastructure
- Conduct statistical analysis and provide actionable business insights
- Mentor junior data scientists and establish best practices
- Present findings to executive leadership and key stakeholders
- Stay current with latest research and industry trends

COMPANY BENEFITS:
- Competitive salary range: $120,000 - $180,000
- Comprehensive health, dental, and vision insurance
- 401(k) with company matching
- Flexible work arrangements and remote options
- Professional development budget ($3,000 annually)
- Conference attendance and training opportunities
- Stock options and performance-based bonuses
- 30 days PTO + holidays
""",

        "frontend_developer_job.txt": """
Frontend Developer - WebDesign Studio

We're looking for a creative Frontend Developer to build amazing user experiences.

REQUIRED SKILLS:
- 2+ years of frontend development experience
- Proficiency in HTML5, CSS3, and JavaScript
- Experience with modern frameworks (React, Vue.js, Angular)
- Knowledge of responsive design and mobile-first development
- Familiarity with version control (Git)
- Understanding of web performance optimization

PREFERRED QUALIFICATIONS:
- Experience with TypeScript
- Knowledge of CSS preprocessors (Sass, Less)
- Familiarity with build tools (Webpack, Vite)
- Experience with testing frameworks (Jest, Cypress)
- Understanding of UX/UI design principles
- Portfolio demonstrating previous work

RESPONSIBILITIES:
- Develop responsive web applications
- Implement UI/UX designs
- Optimize applications for performance
- Collaborate with designers and backend developers
- Ensure cross-browser compatibility
- Maintain code quality and documentation

BENEFITS:
- Creative work environment
- Latest development tools
- Professional growth opportunities
- Flexible schedule
"""
    }
    
    for filename, content in job_descriptions.items():
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(content.strip())
        print(f"✅ Created job description: {filename}")
    
    return len(job_descriptions)

def install_dependencies():
    """Install required Python packages"""
    
    print("📦 Installing Python dependencies...")
    
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      check=True, capture_output=True, text=True)
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        print("Please run: pip install -r requirements.txt")
        return False

def check_ollama():
    """Check if Ollama is available and model is installed"""
    
    print("🔍 Checking Ollama installation...")
    
    try:
        # Check if ollama command is available
        result = subprocess.run(["ollama", "list"], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ Ollama is installed and running")
            
            # Check if llama3.2:3b model is available
            if "llama3.2:3b" in result.stdout:
                print("✅ llama3.2:3b model is available")
                return True
            else:
                print("⚠️  llama3.2:3b model not found")
                print("Please run: ollama pull llama3.2:3b")
                return False
        else:
            print("❌ Ollama is not running")
            print("Please start Ollama: ollama serve")
            return False
            
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("❌ Ollama is not installed or not in PATH")
        print("Please install Ollama from: https://ollama.ai/")
        return False

def main():
    """Main setup function"""
    
    print("🚀 Setting up Resume Parser and Scoring System")
    print("=" * 50)
    
    # Create directories
    from config import create_directories
    create_directories()
    print("✅ Created project directories")
    
    # Install dependencies
    deps_ok = install_dependencies()
    
    # Check Ollama
    ollama_ok = check_ollama()
    
    # Create sample data
    print("\n📄 Creating sample data...")
    resume_count = create_sample_resumes()
    job_count = create_sample_job_descriptions()
    
    print(f"✅ Created {resume_count} sample resumes")
    print(f"✅ Created {job_count} sample job descriptions")
    
    # Final status
    print("\n" + "=" * 50)
    print("📋 SETUP SUMMARY:")
    print(f"   Dependencies: {'✅ OK' if deps_ok else '❌ FAILED'}")
    print(f"   Ollama: {'✅ OK' if ollama_ok else '❌ NEEDS SETUP'}")
    print(f"   Sample Data: ✅ OK")
    
    if deps_ok and ollama_ok:
        print("\n🎉 Setup completed successfully!")
        print("\n📚 QUICK START:")
        print("1. Test the system:")
        print("   python main.py setup")
        print("\n2. Process sample resumes:")
        print("   python main.py process --job-description-file software_developer_job.txt")
        print("\n3. Check results in the 'output/' directory")
    else:
        print("\n⚠️  Setup incomplete. Please resolve the issues above.")
        
        if not ollama_ok:
            print("\n🔧 To fix Ollama issues:")
            print("1. Install Ollama: https://ollama.ai/")
            print("2. Start Ollama: ollama serve")
            print("3. Pull model: ollama pull llama3.2:3b")

if __name__ == "__main__":
    main()
