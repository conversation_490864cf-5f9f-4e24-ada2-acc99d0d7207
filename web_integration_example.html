<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resume Analysis Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }
        
        .candidates-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 20px;
        }
        
        .candidate-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        
        .candidate-card:hover {
            transform: translateY(-5px);
        }
        
        .candidate-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .candidate-name {
            font-size: 1.2em;
            font-weight: bold;
        }
        
        .score-badge {
            padding: 5px 15px;
            border-radius: 20px;
            color: white;
            font-weight: bold;
        }
        
        .score-excellent { background-color: #4CAF50; }
        .score-good { background-color: #2196F3; }
        .score-average { background-color: #FF9800; }
        .score-below { background-color: #f44336; }
        
        .recommendation {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 0.9em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .rec-hire { background-color: #e8f5e8; color: #2e7d32; }
        .rec-consider { background-color: #fff3e0; color: #f57c00; }
        .rec-reject { background-color: #ffebee; color: #c62828; }
        
        .skills-section {
            margin: 15px 0;
        }
        
        .skills-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .skill-tag {
            display: inline-block;
            background-color: #e3f2fd;
            color: #1976d2;
            padding: 3px 8px;
            margin: 2px;
            border-radius: 15px;
            font-size: 0.8em;
        }
        
        .missing-skill {
            background-color: #ffebee;
            color: #c62828;
        }
        
        .score-breakdown {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin: 15px 0;
        }
        
        .score-item {
            text-align: center;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        
        .score-value {
            font-weight: bold;
            color: #667eea;
        }
        
        .load-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            margin-bottom: 20px;
        }
        
        .load-button:hover {
            opacity: 0.9;
        }
        
        .file-input {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Resume Analysis Dashboard</h1>
            <p>AI-Powered Resume Screening Results</p>
        </div>
        
        <div class="file-input">
            <input type="file" id="jsonFile" accept=".json" />
            <button class="load-button" onclick="loadAnalysisData()">Load Analysis Results</button>
        </div>
        
        <div id="dashboard" style="display: none;">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="totalCandidates">0</div>
                    <div>Total Candidates</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="avgScore">0</div>
                    <div>Average Score</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="hireCount">0</div>
                    <div>Recommended to Hire</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="considerCount">0</div>
                    <div>Consider</div>
                </div>
            </div>
            
            <h2 style="margin-bottom: 20px;">Candidate Analysis</h2>
            <div id="candidatesContainer" class="candidates-grid">
                <!-- Candidates will be loaded here -->
            </div>
        </div>
    </div>

    <script>
        let analysisData = null;

        function loadAnalysisData() {
            const fileInput = document.getElementById('jsonFile');
            const file = fileInput.files[0];
            
            if (!file) {
                alert('Please select a JSON file first');
                return;
            }
            
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    analysisData = JSON.parse(e.target.result);
                    displayDashboard();
                } catch (error) {
                    alert('Error parsing JSON file: ' + error.message);
                }
            };
            reader.readAsText(file);
        }

        function displayDashboard() {
            if (!analysisData) return;
            
            // Show dashboard
            document.getElementById('dashboard').style.display = 'block';
            
            // Update statistics
            const stats = analysisData.summary_statistics;
            document.getElementById('totalCandidates').textContent = analysisData.meta.total_candidates;
            document.getElementById('avgScore').textContent = Math.round(stats.average_score);
            document.getElementById('hireCount').textContent = stats.recommendations.HIRE || 0;
            document.getElementById('considerCount').textContent = stats.recommendations.CONSIDER || 0;
            
            // Display candidates
            displayCandidates();
        }

        function displayCandidates() {
            const container = document.getElementById('candidatesContainer');
            container.innerHTML = '';
            
            // Sort candidates by score
            const sortedCandidates = analysisData.candidates
                .filter(c => c.metadata.success)
                .sort((a, b) => b.scores.final_score - a.scores.final_score);
            
            sortedCandidates.forEach(candidate => {
                const card = createCandidateCard(candidate);
                container.appendChild(card);
            });
        }

        function createCandidateCard(candidate) {
            const card = document.createElement('div');
            card.className = 'candidate-card';
            
            const scoreClass = getScoreClass(candidate.scores.final_score);
            const recClass = getRecommendationClass(candidate.recommendation.decision);
            
            card.innerHTML = `
                <div class="candidate-header">
                    <div class="candidate-name">${candidate.candidate_name}</div>
                    <div class="score-badge ${scoreClass}">${candidate.scores.final_score}</div>
                </div>
                
                <div class="recommendation ${recClass}">
                    ${candidate.recommendation.decision}: ${candidate.recommendation.reason.substring(0, 100)}...
                </div>
                
                <div class="score-breakdown">
                    <div class="score-item">
                        <div class="score-value">${candidate.scores.skills_match}</div>
                        <div>Skills</div>
                    </div>
                    <div class="score-item">
                        <div class="score-value">${candidate.scores.experience_score}</div>
                        <div>Experience</div>
                    </div>
                    <div class="score-item">
                        <div class="score-value">${candidate.scores.education_score}</div>
                        <div>Education</div>
                    </div>
                </div>
                
                <div class="skills-section">
                    <div class="skills-title">Matching Skills:</div>
                    ${candidate.skills_analysis.matching_skills.slice(0, 5).map(skill => 
                        `<span class="skill-tag">${skill}</span>`
                    ).join('')}
                </div>
                
                <div class="skills-section">
                    <div class="skills-title">Missing Skills:</div>
                    ${candidate.skills_analysis.missing_skills.slice(0, 3).map(skill => 
                        `<span class="skill-tag missing-skill">${skill}</span>`
                    ).join('')}
                </div>
                
                <div style="margin-top: 15px; font-size: 0.9em; color: #666;">
                    <strong>Interview Focus:</strong> ${candidate.hiring_insights.interview_focus_areas.join(', ')}
                </div>
            `;
            
            return card;
        }

        function getScoreClass(score) {
            if (score >= 90) return 'score-excellent';
            if (score >= 80) return 'score-good';
            if (score >= 60) return 'score-average';
            return 'score-below';
        }

        function getRecommendationClass(recommendation) {
            switch(recommendation) {
                case 'HIRE': return 'rec-hire';
                case 'CONSIDER': return 'rec-consider';
                case 'REJECT': return 'rec-reject';
                default: return 'rec-consider';
            }
        }
    </script>
</body>
</html>
