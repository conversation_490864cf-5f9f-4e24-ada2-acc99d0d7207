"""
Ollama LLM Client for Resume Analysis
Handles communication with Ollama using Python library for resume scoring and analysis
"""

import json
import logging
import time
import threading
from typing import Dict, List, Optional, Any
from concurrent.futures import ThreadPoolExecutor, as_completed

try:
    import ollama
except ImportError:
    ollama = None

from config import OLLAMA_MODEL, OLLAMA_TIMEOUT, OLLAMA_OPTIONS, CHUNK_SIZE, DEFAULT_THREAD_POOL_SIZE

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class OllamaClient:
    """Client for interacting with Ollama using Python library with multi-threading support"""

    def __init__(self, model: str = OLLAMA_MODEL, thread_pool_size: int = DEFAULT_THREAD_POOL_SIZE):
        if ollama is None:
            raise ImportError("ollama library is required. Install with: pip install ollama")

        self.model = model
        self.timeout = OLLAMA_TIMEOUT
        self.options = OLLAMA_OPTIONS.copy()
        self.thread_pool_size = thread_pool_size

        # Thread-local storage for Ollama client instances
        self._local = threading.local()

        # Test connection on initialization
        self._test_connection()

    def _get_client(self):
        """Get thread-local Ollama client instance"""
        if not hasattr(self._local, 'client'):
            self._local.client = ollama
        return self._local.client

    def _test_connection(self) -> bool:
        """Test if Ollama service is available"""
        try:
            client = self._get_client()
            models = client.list()

            if 'models' in models:
                model_names = [model['name'] for model in models['models']]

                if self.model not in model_names:
                    logger.warning(f"Model {self.model} not found. Available models: {model_names}")
                    logger.warning(f"Attempting to pull model {self.model}...")
                    try:
                        client.pull(self.model)
                        logger.info(f"Successfully pulled model {self.model}")
                    except Exception as e:
                        logger.error(f"Failed to pull model {self.model}: {str(e)}")
                        return False

                logger.info(f"Successfully connected to Ollama. Model {self.model} is available.")
                return True
            else:
                logger.error("Failed to get model list from Ollama")
                return False

        except Exception as e:
            logger.error(f"Failed to connect to Ollama: {str(e)}")
            logger.error("Make sure Ollama is running: 'ollama serve'")
            return False
    
    def generate_response(self, prompt: str, system_prompt: str = None) -> Dict[str, Any]:
        """
        Generate response from Ollama model using Python library

        Args:
            prompt (str): User prompt
            system_prompt (str): System prompt for context

        Returns:
            Dict containing response and metadata
        """
        try:
            client = self._get_client()

            # Prepare messages for chat format
            messages = []
            if system_prompt:
                messages.append({
                    'role': 'system',
                    'content': system_prompt
                })

            messages.append({
                'role': 'user',
                'content': prompt
            })

            start_time = time.time()

            # Use chat API for better structured responses
            response = client.chat(
                model=self.model,
                messages=messages,
                options=self.options,
                stream=False
            )

            processing_time = time.time() - start_time

            return {
                'success': True,
                'response': response['message']['content'],
                'processing_time': processing_time,
                'model': self.model,
                'prompt_tokens': len(prompt.split()),
                'error': None
            }

        except Exception as e:
            error_msg = str(e)
            if "connection" in error_msg.lower():
                error_msg = "Could not connect to Ollama service. Is it running?"
            elif "timeout" in error_msg.lower():
                error_msg = f"Request timed out after {self.timeout} seconds"

            return {
                'success': False,
                'response': '',
                'error': error_msg,
                'processing_time': 0
            }
    
    def analyze_resume(self, resume_text: str, job_description: str) -> Dict[str, Any]:
        """
        Analyze resume against job description
        
        Args:
            resume_text (str): Extracted resume text
            job_description (str): Job description to match against
            
        Returns:
            Dict containing analysis results
        """
        
        system_prompt = """You are a resume scoring system. Provide only numerical scores and factual data extraction.

CRITICAL RULES:
1. Respond ONLY with valid JSON - no explanations or text outside JSON
2. Extract skills ONLY if they actually appear in the resume text
3. List missing skills ONLY if they are required but not found in resume
4. Provide numerical scores only - no descriptive text

JSON structure:
{
    "candidate_name": "Extracted name from resume",
    "overall_score": <number 0-100>,
    "skills_match": <number 0-100>,
    "experience_relevance": <number 0-100>,
    "education_match": <number 0-100>,
    "keywords_match": <number 0-100>,
    "overall_fit": <number 0-100>,
    "matching_skills": ["Skills matching job requirements found in resume"],
    "missing_skills": ["Skills required but not found in resume"],
    "matching_experience": ["Relevant experience from resume"],
    "experience_gaps": ["Required experience not found in resume"],
    "education_highlights": ["Education details from resume"],
    "strengths": ["Candidate's strengths based on resume"],
    "weaknesses": ["Gaps or weaknesses in candidate's profile"],
    "growth_potential": <number 0-100>,
    "cultural_fit_indicators": ["Indicators of cultural fit"],
    "salary_expectation_alignment": "LOW|MEDIUM|HIGH",
    "recommendation": "HIRE|CONSIDER|REJECT",
    "recommendation_reason": "Reason for recommendation based on facts",
    "summary": "Summary of candidate's profile",
    "interview_focus_areas": ["Specific areas to focus on during interview"],
    "matching_skills_count": <number>,
    "missing_skills_count": <number>,
    "relevant_experience_years": <number>,
    "education_level_code": <number>  // e.g., 1: Diploma, 2: Bachelor, 3: Master, 4: PhD , 0: Others
}


SKILL MATCHING RULES:
- Only include skills in "matching_skills" if they appear in both resume AND job requirements
- Only include skills in "missing_skills" if they are in job requirements but NOT in resume
- Be exact with skill names - match what's actually written"""

        user_prompt = f"""JOB DESCRIPTION:
{job_description}

RESUME:
{resume_text[:CHUNK_SIZE]}

Extract candidate name and provide numerical scoring only. Match skills exactly as written.

Respond with ONLY the JSON object. No explanations."""

        # Get LLM response
        llm_result = self.generate_response(user_prompt, system_prompt)
        
        if not llm_result['success']:
            return {
                'success': False,
                'error': llm_result['error'],
                'analysis': None,
                'processing_time': llm_result['processing_time']
            }
        
        # Parse JSON response
        try:
            analysis = self._parse_analysis_response(llm_result['response'])
            
            return {
                'success': True,
                'analysis': analysis,
                'processing_time': llm_result['processing_time'],
                'model_used': self.model,
                'error': None
            }
            
        except Exception as e:
            logger.error(f"Error parsing LLM response: {str(e)}")
            return {
                'success': False,
                'error': f'Failed to parse LLM response: {str(e)}',
                'analysis': None,
                'processing_time': llm_result['processing_time']
            }

    def analyze_resumes_batch(self, resume_job_pairs: List[tuple], progress_callback=None) -> List[Dict[str, Any]]:
        """
        Analyze multiple resumes in parallel using thread pool

        Args:
            resume_job_pairs (List[tuple]): List of (resume_text, job_description) tuples
            progress_callback (callable): Optional callback for progress updates

        Returns:
            List of analysis results
        """
        results = []
        total_resumes = len(resume_job_pairs)

        logger.info(f"Starting batch analysis of {total_resumes} resumes using {self.thread_pool_size} threads")

        with ThreadPoolExecutor(max_workers=self.thread_pool_size) as executor:
            # Submit all tasks
            future_to_index = {
                executor.submit(self.analyze_resume, resume_text, job_description): i
                for i, (resume_text, job_description) in enumerate(resume_job_pairs)
            }

            # Initialize results list with placeholders
            results = [None] * total_resumes
            completed = 0

            # Process completed tasks
            for future in as_completed(future_to_index):
                index = future_to_index[future]
                try:
                    result = future.result()
                    results[index] = result
                    completed += 1

                    if progress_callback:
                        progress_callback(completed, total_resumes, result)

                    logger.debug(f"Completed analysis {completed}/{total_resumes}")

                except Exception as e:
                    logger.error(f"Error in batch analysis task {index}: {str(e)}")
                    results[index] = {
                        'success': False,
                        'error': str(e),
                        'analysis': None,
                        'processing_time': 0
                    }
                    completed += 1

        logger.info(f"Batch analysis completed. {len([r for r in results if r and r.get('success')])} successful, {len([r for r in results if r and not r.get('success')])} failed")

        return results

    def analyze_single_threaded(self, resume_text: str, job_description: str) -> Dict[str, Any]:
        """
        Single-threaded analysis for compatibility

        Args:
            resume_text (str): Extracted resume text
            job_description (str): Job description to match against

        Returns:
            Dict containing analysis results
        """
        return self.analyze_resume(resume_text, job_description)
    
    def _parse_analysis_response(self, response: str) -> Dict[str, Any]:
        """Parse and validate LLM analysis response"""
        
        # Try to extract JSON from response
        try:
            # Look for JSON in the response
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            
            if json_start != -1 and json_end != -1:
                json_str = response[json_start:json_end]
                analysis = json.loads(json_str)
            else:
                # If no JSON found, try parsing the entire response
                analysis = json.loads(response)
            
        except json.JSONDecodeError:
            # If JSON parsing fails, create a fallback response
            logger.warning("Could not parse JSON from LLM response, creating fallback")
            analysis = self._create_fallback_analysis(response)
        
        # Validate and normalize the analysis
        return self._validate_analysis(analysis)
    
    def _create_fallback_analysis(self, response: str) -> Dict[str, Any]:
        """Create fallback analysis when JSON parsing fails"""

        # Try to extract some information from text response
        score_match = None
        import re
        score_patterns = [
            r'score[:\s]*(\d+)',
            r'rating[:\s]*(\d+)',
            r'(\d+)[/\s]*100',
            r'(\d+)%'
        ]

        for pattern in score_patterns:
            match = re.search(pattern, response.lower())
            if match:
                try:
                    score_match = int(match.group(1))
                    break
                except:
                    continue

        fallback_score = min(max(score_match or 50, 0), 100)

        return {
            "candidate_name": "Name not found",
            "overall_score": fallback_score,
            "skills_match": fallback_score,
            "experience_relevance": fallback_score,
            "education_match": fallback_score,
            "keywords_match": fallback_score,
            "overall_fit": fallback_score,
            "matching_skills": ["Unable to parse"],
            "missing_skills": ["Unable to parse"],
            "matching_experience": ["Unable to parse"],
            "experience_gaps": ["Unable to parse"],
            "education_highlights": ["Unable to parse"],
            "strengths": ["Analysis parsing failed"],
            "weaknesses": ["Manual review required"],
            "red_flags": ["JSON parsing failed"],
            "growth_potential": fallback_score,
            "cultural_fit_indicators": ["Unable to assess"],
            "salary_expectation_alignment": "MEDIUM",
            "recommendation": "CONSIDER",
            "recommendation_reason": "Analysis parsing failed - manual review required",
            "summary": "Analysis could not be parsed properly",
            "interview_focus_areas": ["Technical assessment", "Experience verification"]
        }
    
    def _validate_analysis(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and normalize analysis results"""

        # Ensure all required fields exist with default values
        defaults = {
            "candidate_name": "Name not found",
            "overall_score": 50,
            "skills_match": 50,
            "experience_relevance": 50,
            "education_match": 50,
            "keywords_match": 50,
            "overall_fit": 50,
            "matching_skills": ["Not specified"],
            "missing_skills": ["Not specified"],
            "matching_experience": ["Not specified"],
            "experience_gaps": ["Not specified"],
            "education_highlights": ["Not specified"],
            "strengths": ["Not specified"],
            "weaknesses": ["Not specified"],
            "red_flags": [],
            "growth_potential": 50,
            "cultural_fit_indicators": ["Not assessed"],
            "salary_expectation_alignment": "MEDIUM",
            "recommendation": "CONSIDER",
            "recommendation_reason": "Standard analysis completed",
            "summary": "Analysis completed",
            "interview_focus_areas": ["General assessment"]
        }

        # Fill missing fields with defaults
        for key, default_value in defaults.items():
            if key not in analysis:
                analysis[key] = default_value

        # Normalize scores to 0-100 range
        score_fields = ["overall_score", "skills_match", "experience_relevance",
                       "education_match", "keywords_match", "overall_fit", "growth_potential"]

        for field in score_fields:
            try:
                score = float(analysis[field])
                analysis[field] = max(0, min(100, score))
            except (ValueError, TypeError):
                analysis[field] = 50

        # Validate recommendation
        valid_recommendations = ["HIRE", "CONSIDER", "REJECT"]
        if analysis["recommendation"] not in valid_recommendations:
            analysis["recommendation"] = "CONSIDER"

        # Validate salary alignment
        valid_salary_alignments = ["LOW", "MEDIUM", "HIGH"]
        if analysis["salary_expectation_alignment"] not in valid_salary_alignments:
            analysis["salary_expectation_alignment"] = "MEDIUM"

        # Ensure lists are actually lists
        list_fields = ["matching_skills", "missing_skills", "matching_experience",
                      "experience_gaps", "education_highlights", "strengths",
                      "weaknesses", "red_flags", "cultural_fit_indicators", "interview_focus_areas"]

        for field in list_fields:
            if not isinstance(analysis[field], list):
                analysis[field] = [str(analysis[field])] if analysis[field] else []

        return analysis


def test_ollama_client():
    """Test function for Ollama client"""
    client = OllamaClient()
    
    sample_resume = """
    John Doe
    Software Developer
    <EMAIL>
    
    Experience:
    - 3 years Python development
    - React and Node.js experience
    - Database management with SQL
    
    Education:
    Bachelor's in Computer Science
    """
    
    sample_job_desc = """
    Software Developer Position
    Required: Python, JavaScript, React, SQL
    Experience: 2-5 years
    Education: Bachelor's degree preferred
    """
    
    result = client.analyze_resume(sample_resume, sample_job_desc)
    print("Test Result:", json.dumps(result, indent=2))


if __name__ == "__main__":
    test_ollama_client()
