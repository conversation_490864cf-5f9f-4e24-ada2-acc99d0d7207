#!/usr/bin/env python3
"""
Test script for multi-threading implementation
Tests the Python Ollama library integration and thread pool performance
"""

import time
import sys
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from ollama_client import OllamaClient
from bulk_processor import BulkProcessor
from config import DEFAULT_JOB_DESCRIPTION

def test_ollama_python_library():
    """Test the Python Ollama library integration"""
    
    print("🧪 Testing Python Ollama Library Integration")
    print("=" * 60)
    
    try:
        # Test basic connection
        client = OllamaClient(thread_pool_size=1)
        print("✅ Ollama client initialized successfully")
        
        # Test single response
        sample_prompt = "Respond with only: {'test': 'success'}"
        system_prompt = "You are a test assistant. Respond only with valid JSON."
        
        start_time = time.time()
        result = client.generate_response(sample_prompt, system_prompt)
        response_time = time.time() - start_time
        
        if result['success']:
            print(f"✅ Single response test successful")
            print(f"   Response time: {response_time:.2f}s")
            print(f"   Model: {result['model']}")
            print(f"   Response length: {len(result['response'])} chars")
        else:
            print(f"❌ Single response test failed: {result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ Ollama library test failed: {str(e)}")
        return False
    
    return True

def test_multithreaded_analysis():
    """Test multi-threaded resume analysis"""
    
    print("\n🧵 Testing Multi-threaded Resume Analysis")
    print("=" * 60)
    
    # Create sample resumes for testing
    sample_resumes = [
        """
        Alice Johnson
        Software Engineer
        <EMAIL>
        
        Experience:
        - 4 years Python development
        - React and JavaScript experience
        - AWS cloud services
        - Team leadership
        
        Education:
        Bachelor's in Computer Science
        """,
        
        """
        Bob Smith
        Data Scientist
        <EMAIL>
        
        Experience:
        - 3 years Python and R
        - Machine learning projects
        - Data visualization
        - Statistical analysis
        
        Education:
        Master's in Data Science
        """,
        
        """
        Carol Davis
        Frontend Developer
        <EMAIL>
        
        Experience:
        - 2 years JavaScript and React
        - UI/UX design
        - Responsive web development
        - CSS and HTML5
        
        Education:
        Associate's in Web Development
        """,
        
        """
        David Wilson
        DevOps Engineer
        <EMAIL>
        
        Experience:
        - 5 years infrastructure management
        - Docker and Kubernetes
        - CI/CD pipelines
        - AWS and Azure
        
        Education:
        Bachelor's in Information Technology
        """,
        
        """
        Eva Martinez
        Full Stack Developer
        <EMAIL>
        
        Experience:
        - 3 years full stack development
        - Python, JavaScript, React
        - Database design
        - API development
        
        Education:
        Bachelor's in Software Engineering
        """
    ]
    
    job_description = """
    Software Developer Position
    Required: Python, JavaScript, React, AWS
    Experience: 2-5 years
    Education: Bachelor's degree preferred
    """
    
    try:
        # Test with different thread pool sizes
        thread_sizes = [1, 3, 5]
        
        for thread_size in thread_sizes:
            print(f"\n📊 Testing with {thread_size} thread(s):")
            
            client = OllamaClient(thread_pool_size=thread_size)
            
            # Prepare resume-job pairs
            resume_job_pairs = [(resume.strip(), job_description) for resume in sample_resumes]
            
            start_time = time.time()
            
            # Test batch processing
            results = client.analyze_resumes_batch(
                resume_job_pairs,
                progress_callback=lambda current, total, result: print(f"   Progress: {current}/{total}")
            )
            
            processing_time = time.time() - start_time
            
            successful = len([r for r in results if r and r.get('success')])
            failed = len([r for r in results if r and not r.get('success')])
            
            print(f"   ✅ Completed in {processing_time:.2f}s")
            print(f"   📈 Successful: {successful}, Failed: {failed}")
            print(f"   ⚡ Avg time per resume: {processing_time/len(sample_resumes):.2f}s")
            
            if thread_size == 1:
                sequential_time = processing_time
            else:
                speedup = sequential_time / processing_time if processing_time > 0 else 0
                print(f"   🚀 Speedup vs sequential: {speedup:.2f}x")
        
        return True
        
    except Exception as e:
        print(f"❌ Multi-threading test failed: {str(e)}")
        return False

def test_bulk_processor_threading():
    """Test the bulk processor with threading"""
    
    print("\n📦 Testing Bulk Processor with Threading")
    print("=" * 60)
    
    # Create temporary resume files
    temp_dir = Path("temp_test_resumes")
    temp_dir.mkdir(exist_ok=True)
    
    sample_resumes = {
        "john_doe.txt": """
        John Doe
        Senior Developer
        <EMAIL>
        
        Experience:
        - 5 years Python development
        - React and Node.js
        - AWS cloud architecture
        - Team leadership
        
        Skills: Python, JavaScript, React, AWS, Docker
        """,
        
        "jane_smith.txt": """
        Jane Smith
        Software Engineer
        <EMAIL>
        
        Experience:
        - 3 years software development
        - Full stack applications
        - Database design
        - API development
        
        Skills: Python, JavaScript, PostgreSQL, Git
        """,
        
        "mike_johnson.txt": """
        Mike Johnson
        DevOps Engineer
        <EMAIL>
        
        Experience:
        - 4 years infrastructure
        - CI/CD pipelines
        - Container orchestration
        - Cloud platforms
        
        Skills: Docker, Kubernetes, AWS, Jenkins
        """
    }
    
    try:
        # Create test files
        for filename, content in sample_resumes.items():
            with open(temp_dir / filename, 'w') as f:
                f.write(content.strip())
        
        job_description = """
        Software Developer Position
        Required: Python, JavaScript, React, AWS
        Experience: 3+ years
        Team leadership preferred
        """
        
        # Test with threading enabled
        print("🧵 Testing with threading enabled:")
        processor_threaded = BulkProcessor(max_workers=3, use_threading=True)
        
        start_time = time.time()
        results_threaded = processor_threaded.process_resumes_directory(
            str(temp_dir),
            job_description,
            progress_callback=lambda current, total, result: print(f"   Threaded progress: {current}/{total}")
        )
        threaded_time = time.time() - start_time
        
        print(f"   ✅ Threaded processing completed in {threaded_time:.2f}s")
        print(f"   📊 Results: {len(results_threaded['results'])} resumes processed")
        
        # Test with threading disabled
        print("\n🔄 Testing with threading disabled:")
        processor_sequential = BulkProcessor(max_workers=1, use_threading=False)
        
        start_time = time.time()
        results_sequential = processor_sequential.process_resumes_directory(
            str(temp_dir),
            job_description,
            progress_callback=lambda current, total, result: print(f"   Sequential progress: {current}/{total}")
        )
        sequential_time = time.time() - start_time
        
        print(f"   ✅ Sequential processing completed in {sequential_time:.2f}s")
        print(f"   📊 Results: {len(results_sequential['results'])} resumes processed")
        
        # Compare performance
        if sequential_time > 0:
            speedup = sequential_time / threaded_time
            print(f"\n🚀 Performance Comparison:")
            print(f"   Threading speedup: {speedup:.2f}x")
            print(f"   Time saved: {sequential_time - threaded_time:.2f}s")
        
        # Cleanup
        import shutil
        shutil.rmtree(temp_dir)
        
        return True
        
    except Exception as e:
        print(f"❌ Bulk processor test failed: {str(e)}")
        # Cleanup on error
        import shutil
        if temp_dir.exists():
            shutil.rmtree(temp_dir)
        return False

def main():
    """Run all multi-threading tests"""
    
    print("🚀 Multi-threading Implementation Test Suite")
    print("=" * 70)
    
    # Test Ollama Python library
    ollama_success = test_ollama_python_library()
    
    # Test multi-threaded analysis
    threading_success = test_multithreaded_analysis()
    
    # Test bulk processor
    bulk_success = test_bulk_processor_threading()
    
    print("\n" + "=" * 70)
    print("📋 TEST SUMMARY:")
    print(f"   Ollama Python Library: {'✅ PASS' if ollama_success else '❌ FAIL'}")
    print(f"   Multi-threaded Analysis: {'✅ PASS' if threading_success else '❌ FAIL'}")
    print(f"   Bulk Processor Threading: {'✅ PASS' if bulk_success else '❌ FAIL'}")
    
    if ollama_success and threading_success and bulk_success:
        print("\n🎉 All multi-threading tests passed!")
        print("\n📈 IMPROVEMENTS VERIFIED:")
        print("   • Python Ollama library integration")
        print("   • Multi-threaded LLM processing")
        print("   • Optimized batch processing")
        print("   • Thread pool management")
        print("   • Performance improvements")
        print("\n💡 USAGE TIPS:")
        print("   • Use --max-workers 5 for optimal performance")
        print("   • Use --disable-threading for debugging")
        print("   • Monitor system resources during processing")
    else:
        print("\n❌ Some tests failed. Please check the error messages above.")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
