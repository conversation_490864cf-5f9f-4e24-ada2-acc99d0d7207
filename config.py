"""
Configuration settings for Resume Parser and Scoring System
"""

import os
from pathlib import Path

# Project paths
PROJECT_ROOT = Path(__file__).parent
RESUMES_DIR = PROJECT_ROOT / "resumes"
OUTPUT_DIR = PROJECT_ROOT / "output"
TEMP_DIR = PROJECT_ROOT / "temp"

# Ollama configuration
OLLAMA_MODEL = "llama3.2:3b"
OLLAMA_TIMEOUT = 120  # seconds
OLLAMA_OPTIONS = {
    'temperature': 0.1,  # Low temperature for consistent scoring
    'top_p': 0.9,
    'num_predict': 1000,  # Max tokens to generate
}

# Multi-threading configuration
DEFAULT_THREAD_POOL_SIZE = 5
MAX_THREAD_POOL_SIZE = 10
MIN_THREAD_POOL_SIZE = 1

# Scoring configuration
MAX_SCORE = 100
MIN_SCORE = 0
PASSING_SCORE = 60

# Supported file formats
SUPPORTED_FORMATS = ['.pdf', '.docx', '.doc', '.txt']

# Resume parsing settings
MAX_FILE_SIZE_MB = 10
CHUNK_SIZE = 1000  # characters for LLM processing

# Output settings
OUTPUT_FORMATS = ['csv', 'json', 'xlsx']
DEFAULT_OUTPUT_FORMAT = 'csv'

# Create necessary directories
def create_directories():
    """Create necessary project directories if they don't exist"""
    directories = [RESUMES_DIR, OUTPUT_DIR, TEMP_DIR]
    for directory in directories:
        directory.mkdir(exist_ok=True)

# Scoring criteria weights (should sum to 1.0)
# Skills matching is now the dominant factor for real-time recruitment
SCORING_WEIGHTS = {
    'skills_match': 0.50,           # 50% - Technical skills alignment (CRITICAL)
    'experience_relevance': 0.20,   # 20% - Work experience relevance
    'education_match': 0.10,        # 10% - Educational background
    'keywords_match': 0.15,         # 15% - Job-specific keywords
    'overall_fit': 0.05             # 5% - General suitability
}

# Critical skills matching thresholds for real-time recruitment
CRITICAL_SKILLS_THRESHOLD = 60     # Minimum skills match score to be considered
MINIMUM_SKILLS_PERCENTAGE = 40     # Minimum % of required skills that must be present
SKILLS_VETO_THRESHOLD = 30         # Below this skills score = automatic REJECT
EXPERIENCE_COMPENSATION_LIMIT = 15  # Max points experience can add if skills are low

# Default job description template
DEFAULT_JOB_DESCRIPTION = """
Python & MySQL Developer - Fresher
📍 Location: Vijayawada, India
🕒 Job Type: Full-Time | Entry-Level
🌟 About the Role
We’re looking for a passionate and inquisitive fresher eager to kick-start their journey in backend development and data-driven applications. If you're comfortable navigating Python’s data structures and dabbling in MySQL queries, this opportunity is your perfect launchpad!

🚀 Key Responsibilities
Build and manage backend logic using Python with a focus on clean data structures

Write efficient queries to interact with MySQL databases for CRUD operations

Collaborate with frontend developers to integrate APIs and ensure seamless data flow

Debug and optimize backend code for performance and scalability

Document processes and assist in deployment pipelines

🧠 Required Skills
Solid understanding of Python fundamentals including lists, dictionaries, sets, tuples, and OOP concepts

Familiarity with basic MySQL queries, joins, and indexing

Exposure to version control systems like Git

Good problem-solving and algorithmic thinking


🤝 What We Offer
Mentorship from senior developers

Real-world projects to build your portfolio

A friendly, collaborative work culture

Opportunities for growth into full-stack development roles
"""
# DEFAULT_JOB_DESCRIPTION = """
# Senior Software Developer Position - TechCorp Solutions

# POSITION OVERVIEW:
# We are seeking a highly skilled Senior Software Developer to join our dynamic engineering team. The ideal candidate will have strong technical expertise, excellent problem-solving abilities, and a passion for building scalable software solutions.

# REQUIRED TECHNICAL SKILLS:
# - Programming Languages: Python (3+ years), JavaScript (ES6+), TypeScript
# - Web Frameworks: React.js, Node.js, Express.js, Django/Flask
# - Database Technologies: PostgreSQL, MongoDB, Redis
# - Cloud Platforms: AWS (EC2, S3, RDS, Lambda), Docker containerization
# - Version Control: Git, GitHub/GitLab workflows
# - API Development: RESTful APIs, GraphQL
# - Testing: Unit testing, Integration testing, TDD practices

# REQUIRED EXPERIENCE:
# - 3-5 years of professional software development experience
# - Experience with Agile/Scrum development methodologies
# - Proven track record of delivering production-ready applications
# - Experience with CI/CD pipelines and DevOps practices
# - Strong understanding of software architecture patterns
# - Experience with code reviews and mentoring junior developers

# REQUIRED EDUCATION & CERTIFICATIONS:
# - Bachelor's degree in Computer Science, Software Engineering, or related technical field
# - Relevant certifications (AWS, Azure, or Google Cloud) preferred

# PREFERRED QUALIFICATIONS:
# - Master's degree in Computer Science or related field
# - Experience with microservices architecture
# - Knowledge of machine learning frameworks (TensorFlow, PyTorch)
# - Experience with mobile development (React Native, Flutter)
# - Familiarity with blockchain technologies
# - Open source contributions

# SOFT SKILLS:
# - Excellent communication and collaboration skills
# - Strong analytical and problem-solving abilities
# - Ability to work independently and in team environments
# - Leadership potential and mentoring capabilities
# - Adaptability to new technologies and frameworks

# RESPONSIBILITIES:
# - Design, develop, and maintain scalable web applications
# - Collaborate with cross-functional teams (Product, Design, QA)
# - Participate in architectural decisions and technical planning
# - Conduct code reviews and ensure code quality standards
# - Troubleshoot and resolve complex technical issues
# - Mentor junior developers and contribute to team knowledge sharing
# - Stay updated with emerging technologies and industry best practices

# COMPANY BENEFITS:
# - Competitive salary range: $90,000 - $130,000
# - Comprehensive health, dental, and vision insurance
# - 401(k) with company matching
# - Flexible work arrangements (hybrid/remote options)
# - Professional development budget ($2,000 annually)
# - Stock options and performance bonuses
# - 25 days PTO + holidays
# """
